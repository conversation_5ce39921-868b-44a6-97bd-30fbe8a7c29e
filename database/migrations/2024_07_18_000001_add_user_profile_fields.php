<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Personal information
            $table->string('first_name')->nullable()->after('name');
            $table->string('last_name')->nullable()->after('first_name');
            $table->date('birth_date')->nullable()->after('last_name');
            $table->enum('gender', ['male', 'female', 'other'])->nullable()->after('birth_date');
            $table->string('phone')->nullable()->unique()->after('gender');
            $table->string('address')->nullable()->after('phone');
            $table->string('city')->nullable()->after('address');
            $table->string('country')->nullable()->after('city');
            
            // Verification fields
            $table->timestamp('phone_verified_at')->nullable()->after('email_verified_at');
            $table->boolean('profile_completed')->default(false)->after('phone_verified_at');
            
            // Video verification
            $table->string('verification_video_path')->nullable()->after('profile_completed');
            $table->timestamp('video_verified_at')->nullable()->after('verification_video_path');
            $table->enum('video_status', ['pending', 'approved', 'rejected'])->default('pending')->after('video_verified_at');
            $table->text('video_rejection_reason')->nullable()->after('video_status');
            
            // Account status
            $table->boolean('is_active')->default(true)->after('video_rejection_reason');
            $table->timestamp('last_login_at')->nullable()->after('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'first_name',
                'last_name', 
                'birth_date',
                'gender',
                'phone',
                'address',
                'city',
                'country',
                'phone_verified_at',
                'profile_completed',
                'verification_video_path',
                'video_verified_at',
                'video_status',
                'video_rejection_reason',
                'is_active',
                'last_login_at'
            ]);
        });
    }
};
