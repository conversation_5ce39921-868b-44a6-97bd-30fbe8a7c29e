<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table) { // input
            $table->id();
            // type ?
            $table->foreignId('worker_id')->index()->constrained('users')->cascadeOnDelete()->cascadeOnUpdate();
            $table->unsignedTinyInteger('status_id')->default(0);
            $table->text('description');
            $table->json('files')->nullable();
            $table->text('result')->nullable();
            $table->json('result_files')->nullable();
            $table->unsignedTinyInteger('rate')->default(0)->comment('task-done rate written, else task can not done');
//            rate, tu dabalia naxui, tuarada ylea patroni da fuls uxdis ;d anu magali nishnavs mis samsax<PERSON>hi shenarchunebas
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};
