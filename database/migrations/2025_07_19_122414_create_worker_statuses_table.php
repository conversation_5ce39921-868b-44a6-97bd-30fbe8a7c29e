<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('worker_statuses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('worker_id')->index()->constrained('workers');
            $table->foreignId('user_id')->index()->constrained('users');
            $table->unsignedBigInteger('status_id')->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('worker_statuses');
    }
};
