<?php

namespace Database\Seeders;

use App\Models\Worker;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class WorkerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing workers
        Worker::query()->delete();

        // Create root workers
        $ceo = Worker::create([
            'position' => 'CEO',
            'slug' => 'ceo',
            'description' => 'Chief Executive Officer',
            'reward' => 10.000
        ]);

        $cto = Worker::create([
            'position' => 'CTO',
            'slug' => 'cto',
            'description' => 'Chief Technology Officer',
            'reward' => 8.500
        ]);

        $cfo = Worker::create([
            'position' => 'CFO',
            'slug' => 'cfo',
            'description' => 'Chief Financial Officer',
            'reward' => 8.500
        ]);

        // Create children under CTO
        $devManager = Worker::create([
            'position' => 'Development Manager',
            'slug' => 'dev-manager',
            'description' => 'Manages development team',
            'reward' => 6.000
        ]);
        $devManager->appendToNode($cto)->save();

        $seniorDev = Worker::create([
            'position' => 'Senior Developer',
            'slug' => 'senior-dev',
            'description' => 'Senior software developer',
            'reward' => 4.500
        ]);
        $seniorDev->appendToNode($devManager)->save();

        $juniorDev = Worker::create([
            'position' => 'Junior Developer',
            'slug' => 'junior-dev',
            'description' => 'Junior software developer',
            'reward' => 2.500
        ]);
        $juniorDev->appendToNode($devManager)->save();

        // Create children under CFO
        $accountingManager = Worker::create([
            'position' => 'Accounting Manager',
            'slug' => 'accounting-manager',
            'description' => 'Manages accounting department',
            'reward' => 5.500
        ]);
        $accountingManager->appendToNode($cfo)->save();

        $accountant = Worker::create([
            'position' => 'Accountant',
            'slug' => 'accountant',
            'description' => 'Financial accountant',
            'reward' => 3.000
        ]);
        $accountant->appendToNode($accountingManager)->save();

        // Create some more hierarchy under CEO
        $hrManager = Worker::create([
            'position' => 'HR Manager',
            'slug' => 'hr-manager',
            'description' => 'Human Resources Manager',
            'reward' => 5.000
        ]);
        $hrManager->appendToNode($ceo)->save();

        $hrSpecialist = Worker::create([
            'position' => 'HR Specialist',
            'slug' => 'hr-specialist',
            'description' => 'Human Resources Specialist',
            'reward' => 3.500
        ]);
        $hrSpecialist->appendToNode($hrManager)->save();

        echo "Worker hierarchy created successfully!\n";
    }
}
