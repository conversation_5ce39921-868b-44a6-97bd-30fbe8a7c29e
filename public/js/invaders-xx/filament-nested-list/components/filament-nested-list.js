function L(s,t){if(!(s instanceof t))throw new TypeError("Cannot call a class as a function")}function y(s,t){for(var e=0;e<t.length;e++){var i=t[e];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(s,I(i.key),i)}}function N(s,t,e){return t&&y(s.prototype,t),e&&y(s,e),Object.defineProperty(s,"prototype",{writable:!1}),s}function d(s,t,e){return t=I(t),t in s?Object.defineProperty(s,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):s[t]=e,s}function u(s){return T(s)||A(s)||C(s)||S()}function T(s){if(Array.isArray(s))return f(s)}function A(s){if(typeof Symbol<"u"&&s[Symbol.iterator]!=null||s["@@iterator"]!=null)return Array.from(s)}function C(s,t){if(s){if(typeof s=="string")return f(s,t);var e=Object.prototype.toString.call(s).slice(8,-1);if(e==="Object"&&s.constructor&&(e=s.constructor.name),e==="Map"||e==="Set")return Array.from(s);if(e==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return f(s,t)}}function f(s,t){(t==null||t>s.length)&&(t=s.length);for(var e=0,i=new Array(t);e<t;e++)i[e]=s[e];return i}function S(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function M(s,t){if(typeof s!="object"||s===null)return s;var e=s[Symbol.toPrimitive];if(e!==void 0){var i=e.call(s,t||"default");if(typeof i!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(s)}function I(s){var t=M(s,"string");return typeof t=="symbol"?t:String(t)}var w=function(){function s(t){var e=t.data,i=t.propertyMap,r=i===void 0?{}:i,n=t.renderListItem;L(this,s),d(this,"data",void 0),d(this,"sortedData",void 0),d(this,"sortedDataDomArray",void 0),d(this,"propertyMap",void 0),d(this,"renderListItem",void 0),d(this,"boundGetItemPropProxyName",void 0),this.data=e,this.sortedData=[],this.sortedDataDomArray=[],this.propertyMap=r,this.renderListItem=n,this.boundGetItemPropProxyName=this.getItemPropProxyName.bind(this),this.maybeTransformData()}return N(s,[{key:"addMappingProxyToItem",value:function(e){var i=this;return new Proxy(e,{get:function(n,a,o){return Reflect.get(n,i.boundGetItemPropProxyName(a),o)}})}},{key:"maybeTransformData",value:function(){!Object.keys(this.propertyMap).length||!Array.isArray(this.data)||(this.data=this.data.map(this.addMappingProxyToItem.bind(this)))}},{key:"getItemPropProxyName",value:function(e){return Object.prototype.hasOwnProperty.call(this.propertyMap,e)?this.propertyMap[e]:e}},{key:"isTopLevelItem",value:function(e){return!e.parent}},{key:"sortListItems",value:function(){var e=this,i=u(this.data),r=i.filter(function(a){return e.isTopLevelItem(a)}).sort(function(a,o){return a.order&&o.order?a.order-o.order:0}),n=i.filter(function(a){return!e.isTopLevelItem(a)}).reduce(function(a,o){return o.parent&&(Object.prototype.hasOwnProperty.call(a,o.parent)?a[o.parent].push(o):a[o.parent]=[o]),a},{});return Object.keys(n).forEach(function(a){n[a].sort(function(o,l){return o.order&&l.order?o.order-l.order:0})}),this.sortedData=[].concat(u(r),u(Object.values(n).flat())),this.sortedData}},{key:"addNewItem",value:function(e){var i=e.item,r=e.asLastChild,n=r===void 0?!1:r,a=this.addMappingProxyToItem(i);return Array.isArray(this.data)&&this.data[n?"push":"unshift"](a),this.createItemElement(a)}},{key:"createItemElement",value:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"li",r=e.id,n=e.text,a=document.createElement(i);return a.dataset.id=r,i==="li"&&n&&(a.innerHTML=n),i==="li"&&typeof this.renderListItem=="function"?this.renderListItem(a,e):a}},{key:"elementIsParentOfItem",value:function(e,i){return e.dataset.id==="".concat(i.parent)}},{key:"getParentNodeOfItem",value:function(e,i,r){return e.querySelector("".concat(r,'[data-id="').concat(i.parent,'"]'))}},{key:"elementIsAncestorOfItem",value:function(e,i){return!!this.getParentNodeOfItem(e,i,"li")}},{key:"getDirectListParentOfItem",value:function(e,i){return this.getParentNodeOfItem(e,i,"ol")}},{key:"maybeAppendItemToParentDom",value:function(e){var i=this,r=e.parent,n=this.sortedDataDomArray.find(function(h){return i.elementIsParentOfItem(h,e)||i.elementIsAncestorOfItem(h,e)});if(!n)return!1;var a=this.createItemElement(e),o=this.getDirectListParentOfItem(n,e);if(!o){o=this.createItemElement({id:r},"ol");var l=this.getParentNodeOfItem(n,e,"li")||n;l.appendChild(o)}return o.appendChild(a),!0}},{key:"getListItemsDom",value:function(){var e=this;this.sortedDataDomArray=[];for(var i=[];i.length!==this.sortListItems().length;)i=this.sortedData.reduce(function(r,n){if(!n.id)return r;var a=n.id.toString();if(r.includes(a))return r;var o;if(n.parent)o=e.maybeAppendItemToParentDom(n);else{var l=e.createItemElement(n);e.sortedDataDomArray.push(l),o=!0}return o&&r.push(a),r},i);return this.sortedDataDomArray}},{key:"convertDomToData",value:function(e){var i=this;return Array.from(e?.querySelectorAll("li")||[]).map(function(r){var n,a=r.parentNode,o=a.dataset.id,l=Array.from(a.children).findIndex(function(h){return h===r})+1;return n={},d(n,i.getItemPropProxyName("id"),r.dataset.id),d(n,i.getItemPropProxyName("parent"),o),d(n,i.getItemPropProxyName("order"),l),n})}},{key:"render",value:function(){var e=document.createElement("ol");return this.getListItemsDom().forEach(function(i){return e.appendChild(i)}),e}}]),s}(),b=function(){function s(t){var e=t.actions,i=e===void 0?{}:e,r=t.data,n=t.droppingEdge,a=n===void 0?15:n,o=t.el,l=t.init,h=l===void 0?!0:l,c=t.listClassNames,D=t.listItemClassNames,E=t.nestingLevels,m=t.propertyMap,P=m===void 0?{}:m,k=t.renderListItem;L(this,s),d(this,"actions",void 0),d(this,"classNames",void 0),d(this,"cursor",void 0),d(this,"data",void 0),d(this,"dataEngine",void 0),d(this,"distances",void 0),d(this,"draggedNode",void 0),d(this,"initialised",void 0),d(this,"listClassNames",void 0),d(this,"listEventListeners",void 0),d(this,"listInterface",void 0),d(this,"listItemClassNames",void 0),d(this,"mainListClassName",void 0),d(this,"nestingLevels",void 0),d(this,"placeholderList",void 0),d(this,"placeholderInUse",void 0),d(this,"propertyMap",void 0),d(this,"renderListItem",void 0),d(this,"sortableList",void 0),d(this,"targetedNode",void 0),d(this,"targetNode",void 0),d(this,"wrapper",void 0),this.renderListItem=k;var g=typeof o=="string"?document.querySelector(o):o,v=g instanceof HTMLOListElement||g instanceof HTMLUListElement;this.wrapper=v?void 0:g,this.sortableList=v?g:null,this.data=r,this.listClassNames=this.createListClassNamesArray(c),this.mainListClassName=this.listClassNames[0]||"nested-sort",this.listItemClassNames=this.createListClassNamesArray(D),this.propertyMap=P,this.actions={onDrop:i.onDrop},this.initialised=!1,this.distances={droppingEdge:a},this.classNames={dragged:"ns-dragged",placeholder:"ns-placeholder",targeted:"ns-targeted"},this.listEventListeners={dragover:this.onDragOver.bind(this),dragstart:this.onDragStart.bind(this),dragenter:this.onDragEnter.bind(this),dragend:this.onDragEnd.bind(this),drop:this.onDrop.bind(this)};var p=parseInt(E);this.nestingLevels=isNaN(p)?-1:p,this.listInterface=this.getListInterface(),this.maybeInitDataDom(),this.addListAttributes(),h&&this.initDragAndDrop()}return N(s,[{key:"getListInterface",value:function(){return Array.isArray(this.data)&&this.data.length||this.sortableList instanceof HTMLOListElement?HTMLOListElement:HTMLUListElement}},{key:"getDataEngine",value:function(){return this.dataEngine?this.dataEngine:(this.dataEngine=new w({data:this.data,propertyMap:this.propertyMap,renderListItem:this.renderListItem}),this.dataEngine)}},{key:"createListClassNamesArray",value:function(e){return e?Array.isArray(e)?e:e.split(" "):[]}},{key:"maybeInitDataDom",value:function(){if(Array.isArray(this.data)&&this.data.length&&this.wrapper){var e=this.getDataEngine().render();this.wrapper.innerHTML="",this.wrapper.appendChild(e),this.sortableList=e}}},{key:"getListTagName",value:function(){return this.listInterface===HTMLOListElement?"ol":"ul"}},{key:"getSortableList",value:function(){var e;return this.sortableList instanceof this.listInterface?this.sortableList:(this.sortableList=(e=this.wrapper)===null||e===void 0?void 0:e.querySelector(this.getListTagName()),this.sortableList)}},{key:"addListAttributes",value:function(){var e,i=this,r=this.getSortableList();r&&((e=r.classList).add.apply(e,u(this.listClassNames.concat(this.mainListClassName))),r.querySelectorAll(this.getListTagName()).forEach(function(n){var a;(a=n.classList).add.apply(a,u(i.listClassNames))}),r.querySelectorAll("li").forEach(function(n){var a;(a=n.classList).add.apply(a,u(i.listItemClassNames))}))}},{key:"toggleMainListLifeCycleClassName",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,i=this.getSortableList();if(i){var r="".concat(this.mainListClassName,"--enabled");e?i.classList.add(r):i.classList.remove(r)}}},{key:"toggleListItemAttributes",value:function(){var e,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;(e=this.getSortableList())===null||e===void 0||e.querySelectorAll("li").forEach(function(r){r.setAttribute("draggable",i.toString())})}},{key:"toggleListEventListeners",value:function(){var e=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,r=this.getSortableList();r&&Object.keys(this.listEventListeners).forEach(function(n){i?r.removeEventListener(n,e.listEventListeners[n]):r.addEventListener(n,e.listEventListeners[n],!1)})}},{key:"initDragAndDrop",value:function(){this.initialised||(this.toggleListEventListeners(),this.initPlaceholderList(),this.toggleListItemAttributes(),this.toggleMainListLifeCycleClassName(),this.initialised=!0)}},{key:"init",value:function(){this.initDragAndDrop()}},{key:"destroy",value:function(){this.toggleListEventListeners(!0),this.toggleListItemAttributes(!1),this.toggleMainListLifeCycleClassName(!1),this.initialised=!1}},{key:"removeClassFromEl",value:function(e,i){i&&i.classList.contains(e)&&i.classList.remove(e)}},{key:"canBeTargeted",value:function(e){return!this.draggedNode||this.draggedNode===e?!1:e.nodeName==="LI"?!this.nestingThresholdReached(e):e instanceof this.listInterface&&e.classList.contains(this.classNames.placeholder)}},{key:"onDragStart",value:function(e){var i;this.draggedNode=e.target,this.draggedNode.classList.add(this.classNames.dragged),(i=e.dataTransfer)===null||i===void 0||i.setData("text","")}},{key:"onDragOver",value:function(e){e.preventDefault(),this.updateCoordination(e),this.managePlaceholderLists()}},{key:"onDragEnter",value:function(e){this.canBeTargeted(e.target)&&(this.removeClassFromEl(this.classNames.targeted,this.targetedNode),this.targetedNode=e.target,this.targetedNode.classList.add(this.classNames.targeted))}},{key:"onDragEnd",value:function(e){e.stopPropagation(),this.removeClassFromEl(this.classNames.dragged,this.draggedNode),this.removeClassFromEl(this.classNames.targeted,this.targetedNode),this.cleanupPlaceholderLists(),delete this.draggedNode,delete this.targetedNode}},{key:"onDrop",value:function(e){e.stopPropagation(),this.maybeDrop(),this.cleanupPlaceholderLists(),typeof this.actions.onDrop=="function"&&this.actions.onDrop(this.getDataEngine().convertDomToData(this.getSortableList()))}},{key:"updateCoordination",value:function(e){this.calcMouseCoords(e),this.calcMouseToTargetedElDist()}},{key:"getDropLocation",value:function(){if(this.canBeDropped()){var e;if(((e=this.targetedNode)===null||e===void 0?void 0:e.nodeName)==="LI")return"before";if(this.targetedNode instanceof this.listInterface)return"inside"}}},{key:"maybeDrop",value:function(){var e=this.getDropLocation();e&&this.dropTheItem(e)}},{key:"dropTheItem",value:function(e){var i,r,n;switch(e){case"before":(i=this.targetedNode)===null||i===void 0||(r=i.parentNode)===null||r===void 0||r.insertBefore(this.draggedNode,this.targetedNode);break;case"inside":(n=this.targetedNode)===null||n===void 0||n.appendChild(this.draggedNode);break}}},{key:"calcMouseCoords",value:function(e){this.cursor={X:e.clientX,Y:e.clientY}}},{key:"calcMouseToTargetedElDist",value:function(){if(this.targetedNode){var e=this.targetedNode.getBoundingClientRect();this.targetNode={X:e.left,Y:e.top};var i=this.targetNode.Y-this.cursor.Y,r=Math.abs(i);this.distances.mouseTo={targetedElTop:i,targetedElTopAbs:r,targetedElBot:r-this.targetedNode.clientHeight}}}},{key:"areNested",value:function(e,i){return!!e&&!!i&&Array.from(i?.querySelectorAll("li")).some(function(r){return r===e})}},{key:"cursorIsIndentedEnough",value:function(){return this.cursor.X-this.targetNode.X>50}},{key:"mouseIsTooCloseToTop",value:function(){var e;return(e=this.distances)!==null&&e!==void 0&&e.droppingEdge?this.cursor.Y-this.targetNode.Y<this.distances.droppingEdge:!1}},{key:"managePlaceholderLists",value:function(){var e=this,i=this.analysePlaceHolderSituation();i.forEach(function(r){switch(r){case"add":e.cleanupPlaceholderLists(),e.addPlaceholderList();break;case"cleanup":e.cleanupPlaceholderLists();break}})}},{key:"targetedNodeIsPlaceholder",value:function(){return this.targetedNode instanceof this.listInterface&&this.targetedNode.classList.contains(this.classNames.placeholder)}},{key:"getNodeDepth",value:function(e){var i=0,r=this.getSortableList(),n=0;if(this.draggedNode){var a=this.draggedNode.querySelectorAll("ul").length||0,o=this.draggedNode.querySelectorAll("ol").length||0;n=a>o?a:o}for(;r!==((l=e)===null||l===void 0?void 0:l.parentElement);){var l,h,c;((h=e)===null||h===void 0?void 0:h.parentElement)instanceof this.listInterface&&i++,e=(c=e)===null||c===void 0?void 0:c.parentElement}return i+n}},{key:"nestingThresholdReached",value:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return this.nestingLevels<0?!1:i?this.getNodeDepth(e)>=this.nestingLevels:this.getNodeDepth(e)>this.nestingLevels}},{key:"analysePlaceHolderSituation",value:function(){if(!this.targetedNode||this.areNested(this.targetedNode,this.draggedNode))return[];var e=[];return!this.cursorIsIndentedEnough()||this.mouseIsTooCloseToTop()?this.targetedNodeIsPlaceholder()||e.push("cleanup"):this.targetedNode!==this.draggedNode&&this.targetedNode.nodeName==="LI"&&!this.targetedNode.querySelectorAll(this.getListTagName()).length&&!this.nestingThresholdReached(this.targetedNode,!0)&&e.push("add"),e}},{key:"animatePlaceholderList",value:function(){var e;this.placeholderInUse.style.minHeight="0",this.placeholderInUse.style.transition="min-height ease .2s",this.placeholderInUse.style.minHeight="".concat((e=this.draggedNode)===null||e===void 0?void 0:e.offsetHeight,"px")}},{key:"addPlaceholderList",value:function(){var e;this.getPlaceholderList(),(e=this.targetedNode)===null||e===void 0||e.appendChild(this.placeholderInUse),this.animatePlaceholderList()}},{key:"targetNodeIsIdentified",value:function(){return!!this.targetedNode}},{key:"targetNodeIsBeingDragged",value:function(){return this.targetNodeIsIdentified()&&this.targetedNode===this.draggedNode}},{key:"targetNodeIsListWithItems",value:function(){return this.targetNodeIsIdentified()&&this.targetedNode instanceof this.listInterface&&!!this.targetedNode.querySelectorAll("li").length}},{key:"canBeDropped",value:function(){return this.targetNodeIsIdentified()&&!this.targetNodeIsBeingDragged()&&!this.targetNodeIsListWithItems()&&!this.areNested(this.targetedNode,this.draggedNode)}},{key:"cleanupPlaceholderLists",value:function(){var e,i=this,r=this.getListTagName(),n=((e=this.getSortableList())===null||e===void 0?void 0:e.querySelectorAll(r))||[];Array.from(n).forEach(function(a){a.querySelectorAll("li").length?a.classList.contains(i.classNames.placeholder)&&(a.classList.remove(i.classNames.placeholder),a.style.minHeight="auto",a.dataset.id=a.parentNode.dataset.id):a.remove()})}},{key:"initPlaceholderList",value:function(){var e;this.placeholderList=document.createElement(this.getListTagName()),(e=this.placeholderList.classList).add.apply(e,[this.classNames.placeholder].concat(u(this.listClassNames)))}},{key:"getPlaceholderList",value:function(){return this.placeholderInUse=this.placeholderList.cloneNode(!0),this.placeholderInUse}},{key:"addNewItem",value:function(e){var i,r=e.item,n=e.asLastChild,a=n===void 0?!1:n,o=this.getDataEngine().addNewItem({item:r,asLastChild:a});return o.setAttribute("draggable",String(this.initialised)),(i=this.getSortableList())===null||i===void 0||i[a?"append":"prepend"](o),{data:this.getDataEngine().convertDomToData(this.getSortableList())}}}]),s}();function O({items:s=[],maxDepth:t=2,selector:e="#nestedList"}){return{items:s,init(){let i=this,r=`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z" />
</svg>`,n=new b({el:e,data:i.items,listClassNames:"fi-nested-list",listItemClassNames:"fi-nested-list-item",nestingLevels:t,propertyMap:{parent:"parent_id"},renderListItem:(a,{id:o,parent_id:l,text:h})=>(a.innerHTML='<div class="rounded-lg border h-10 flex w-full items-center border-gray-300 bg-white dark:border-white/10 dark:bg-gray-800"><div class="w-full h-full flex flex-row items-center rounded-lg px-px bg-gray-50 border-gray-300 dark:bg-gray-900 dark:border-white/10"><span class="text-gray-400 dark:text-gray-500">'+r+'</span><div class="gap-1 ml-4 rtl:mr-4">'+a.innerHTML+"</div></div></div>",a),actions:{onDrop:function(a){i.items=a}}})},save(){this.$wire.updateNestedList(this.items)}}}export{O as default};
