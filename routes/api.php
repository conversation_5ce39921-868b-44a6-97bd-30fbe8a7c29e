<?php

use App\Http\Controllers\NetworkController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\UserController;
use App\Http\Controllers\API\VerificationController;
use App\Http\Controllers\API\PasswordResetController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/forgot-password', [PasswordResetController::class, 'forgotPassword']);
Route::post('/reset-password', [PasswordResetController::class, 'resetPassword']);

// Email verification
Route::post('/email/verify/send', [VerificationController::class, 'sendEmailVerification']);
Route::post('/email/verify/{id}/{hash}', [VerificationController::class, 'verifyEmail'])->name('verification.verify');

// Phone verification
Route::post('/phone/verify/send', [VerificationController::class, 'sendPhoneVerification']);
Route::post('/phone/verify', [VerificationController::class, 'verifyPhone']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // User profile
    Route::get('/user', [UserController::class, 'show']);
    Route::put('/user', [UserController::class, 'update']);
    Route::post('/user/video', [UserController::class, 'uploadVideo']);

    // Logout
    Route::post('/logout', [AuthController::class, 'logout']);
});




Route::middleware('auth:sanctum')->group(function () {
    Route::get('/lands', [NetworkController::class, 'lands']);
});
