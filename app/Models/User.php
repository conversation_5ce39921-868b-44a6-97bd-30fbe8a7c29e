<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'first_name',
        'last_name',
        'email',
        'password',
        'phone',
        'birth_date',
        'gender',
        'address',
        'city',
        'country',
        'verification_video_path',
        'profile_completed',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'phone_verified_at' => 'datetime',
            'video_verified_at' => 'datetime',
            'last_login_at' => 'datetime',
            'birth_date' => 'date',
            'password' => 'hashed',
            'profile_completed' => 'boolean',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the phone verifications for the user.
     */
    public function phoneVerifications()
    {
        return $this->hasMany(PhoneVerification::class, 'phone', 'phone');
    }

    /**
     * Get the email verifications for the user.
     */
    public function emailVerifications()
    {
        return $this->hasMany(EmailVerification::class, 'email', 'email');
    }

    /**
     * Check if user has completed profile
     */
    public function hasCompletedProfile(): bool
    {
        return !empty($this->first_name) &&
               !empty($this->last_name) &&
               !empty($this->phone) &&
               !empty($this->birth_date) &&
               !empty($this->verification_video_path) &&
               $this->email_verified_at !== null &&
               $this->phone_verified_at !== null;
    }

    /**
     * Check if user is fully verified
     */
    public function isFullyVerified(): bool
    {
        return $this->hasCompletedProfile() &&
               $this->video_status === 'approved';
    }
}
