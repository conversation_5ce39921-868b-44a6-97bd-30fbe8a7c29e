<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Support\Str;

class EmailVerification extends Model
{
    use HasFactory;

    protected $fillable = [
        'email',
        'token',
        'expires_at',
        'verified',
        'verified_at',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'verified_at' => 'datetime',
        'verified' => 'boolean',
    ];

    /**
     * Generate a new verification token
     */
    public static function generateToken(): string
    {
        return Str::random(64);
    }

    /**
     * Create a new verification record
     */
    public static function createForEmail(string $email): self
    {
        // Delete any existing unverified tokens for this email
        self::where('email', $email)
            ->where('verified', false)
            ->delete();

        return self::create([
            'email' => $email,
            'token' => self::generateToken(),
            'expires_at' => Carbon::now()->addHours(24), // 24 hours expiry
            'verified' => false,
        ]);
    }

    /**
     * Check if the token is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Mark as verified
     */
    public function markAsVerified(): void
    {
        $this->update([
            'verified' => true,
            'verified_at' => Carbon::now(),
        ]);
    }

    /**
     * Scope for active (non-expired, non-verified) tokens
     */
    public function scopeActive($query)
    {
        return $query->where('verified', false)
                    ->where('expires_at', '>', Carbon::now());
    }
}
