<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WorkerStatus extends Model
{
    protected $table = 'worker_statuses';

    protected $fillable = [
        'worker_id',
        'user_id',
        'status_id',
    ];

    public function worker(): BelongsT<PERSON>
    {
        return $this->belongsTo(Worker::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
