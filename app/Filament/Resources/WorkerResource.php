<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WorkerResource\Pages;
use App\Filament\Resources\WorkerResource\RelationManagers;
use App\Models\Worker;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Tables\Filters\SelectFilter;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WorkerResource extends Resource
{
    protected static ?string $model = Worker::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationLabel = 'Workers';

    protected static ?string $modelLabel = 'Worker';

    protected static ?string $pluralModelLabel = 'Workers';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('position')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                                if ($operation !== 'create') {
                                    return;
                                }
                                $set('slug', \Illuminate\Support\Str::slug($state));
                            }),
                        Forms\Components\TextInput::make('slug')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        Forms\Components\Textarea::make('description')
                            ->columnSpanFull()
                            ->rows(3),
                        Forms\Components\TextInput::make('reward')
                            ->required()
                            ->numeric()
                            ->step(0.001)
                            ->suffix('%')
                            ->helperText('Reward percentage (e.g., 5.250 for 5.25%)'),
                    ])->columns(2),

                Forms\Components\Section::make('Hierarchy')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->nullable()
                            ->helperText('Assign a user to this worker position'),
                        Forms\Components\Select::make('parent_id')
                            ->label('Parent Worker')
                            ->options(function () {
                                return Worker::query()
                                    ->orderBy('position')
                                    ->pluck('position', 'id');
                            })
                            ->searchable()
                            ->nullable()
                            ->helperText('Select parent worker in hierarchy'),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('position')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                Tables\Columns\TextColumn::make('parent.position')
                    ->label('Parent')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Root Level')
                    ->badge()
                    ->color('gray'),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Assigned User')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Unassigned')
                    ->badge()
                    ->color(fn ($state) => $state ? 'success' : 'warning'),
                Tables\Columns\TextColumn::make('reward')
                    ->label('Reward %')
                    ->numeric(decimalPlaces: 3)
                    ->sortable()
                    ->suffix('%')
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('children_count')
                    ->label('Children')
                    ->counts('children')
                    ->badge()
                    ->color('info')
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('depth')
                    ->label('Level')
                    ->badge()
                    ->color(fn ($state) => match($state) {
                        0 => 'success',
                        1 => 'warning',
                        default => 'danger'
                    })
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('_lft', 'asc')
            ->filters([
                SelectFilter::make('parent_id')
                    ->label('Parent Worker')
                    ->options(function () {
                        return Worker::query()
                            ->orderBy('position')
                            ->pluck('position', 'id');
                    })
                    ->placeholder('All Workers'),
                SelectFilter::make('user_id')
                    ->label('Assigned User')
                    ->relationship('user', 'name')
                    ->placeholder('All Users'),
                SelectFilter::make('depth')
                    ->label('Hierarchy Level')
                    ->options([
                        0 => 'Root Level',
                        1 => 'Level 1',
                        2 => 'Level 2',
                        3 => 'Level 3+',
                    ])
                    ->placeholder('All Levels'),
            ])
            ->actions([
                Action::make('attach_to_tree')
                    ->label('Attach to Tree')
                    ->icon('heroicon-o-link')
                    ->color('success')
                    ->form([
                        Forms\Components\Select::make('parent_id')
                            ->label('Select Parent Worker')
                            ->options(function ($record) {
                                return Worker::query()
                                    ->where('id', '!=', $record->id)
                                    ->orderBy('position')
                                    ->pluck('position', 'id');
                            })
                            ->searchable()
                            ->required()
                            ->helperText('Choose which worker this should be attached under'),
                    ])
                    ->action(function (Worker $record, array $data) {
                        $parent = Worker::find($data['parent_id']);
                        if ($parent) {
                            $record->appendToNode($parent)->save();
                            Notification::make()
                                ->title('Worker attached successfully')
                                ->success()
                                ->send();
                        }
                    }),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ChildrenRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWorkers::route('/'),
            'create' => Pages\CreateWorker::route('/create'),
            'view' => Pages\ViewWorker::route('/{record}'),
            'edit' => Pages\EditWorker::route('/{record}/edit'),
        ];
    }
}
