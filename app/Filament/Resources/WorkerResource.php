<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WorkerResource\Pages;
use App\Filament\Resources\WorkerResource\RelationManagers;
use App\Models\Worker;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class WorkerResource extends Resource
{
    protected static ?string $model = Worker::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationLabel = 'Workers';

    protected static ?string $modelLabel = 'Worker';

    protected static ?string $pluralModelLabel = 'Workers';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Worker Information')
                    ->schema([
                        Forms\Components\TextInput::make('position')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                                if ($operation !== 'create') {
                                    return;
                                }
                                $set('slug', Str::slug($state));
                            }),

                        Forms\Components\TextInput::make('slug')
                            ->required()
                            ->maxLength(255)
                            ->unique(Worker::class, 'slug', ignoreRecord: true)
                            ->rules(['alpha_dash']),

                        Forms\Components\Textarea::make('description')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\Select::make('user_id')
                            ->label('Assigned User')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('email')
                                    ->email()
                                    ->required()
                                    ->maxLength(255),
                            ]),

                        Forms\Components\TextInput::make('reward')
                            ->label('Reward (%)')
                            ->required()
                            ->numeric()
                            ->step(0.001)
                            ->minValue(0)
                            ->maxValue(100)
                            ->suffix('%'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Hierarchy')
                    ->schema([
                        Forms\Components\Select::make('parent_id')
                            ->label('Parent Worker')
                            ->options(function ($record) {
                                $query = Worker::query();
                                if ($record) {
                                    // Exclude current record and its descendants
                                    $query->where('id', '!=', $record->id);
                                    if ($record->exists) {
                                        $descendantIds = $record->descendants()->pluck('id')->toArray();
                                        if (!empty($descendantIds)) {
                                            $query->whereNotIn('id', $descendantIds);
                                        }
                                    }
                                }
                                return $query->get()->pluck('position', 'id');
                            })
                            ->searchable()
                            ->placeholder('Select parent worker (leave empty for root level)'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('position')
                    ->label('Position')
                    ->searchable()
                    ->sortable()
                    ->formatStateUsing(function ($record) {
                        $indent = str_repeat('—', $record->depth ?? 0);
                        return $indent . ' ' . $record->position;
                    }),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Assigned User')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Not assigned'),

                Tables\Columns\TextColumn::make('reward')
                    ->label('Reward')
                    ->numeric()
                    ->sortable()
                    ->suffix('%')
                    ->alignEnd(),

                Tables\Columns\TextColumn::make('parent.position')
                    ->label('Parent')
                    ->searchable()
                    ->placeholder('Root level'),

                Tables\Columns\TextColumn::make('children_count')
                    ->label('Children')
                    ->counts('children')
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('tasks_count')
                    ->label('Tasks')
                    ->counts('tasks')
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('parent_id')
                    ->label('Parent Worker')
                    ->options(Worker::whereNotNull('parent_id')->with('parent')->get()->pluck('parent.position', 'parent_id'))
                    ->placeholder('All levels'),

                Tables\Filters\SelectFilter::make('user_id')
                    ->label('Assigned User')
                    ->relationship('user', 'name')
                    ->placeholder('All users'),

                Tables\Filters\Filter::make('has_children')
                    ->label('Has Children')
                    ->query(fn (Builder $query): Builder => $query->has('children')),

                Tables\Filters\Filter::make('root_only')
                    ->label('Root Level Only')
                    ->query(fn (Builder $query): Builder => $query->whereIsRoot()),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->before(function (Worker $record) {
                        // Move children to parent before deleting
                        if ($record->children()->exists()) {
                            $record->children()->update(['parent_id' => $record->parent_id]);
                        }
                    }),

                Tables\Actions\Action::make('add_child')
                    ->label('Add Child')
                    ->icon('heroicon-o-plus')
                    ->url(fn (Worker $record): string => route('filament.admin.resources.workers.create', ['parent' => $record->id]))
                    ->color('success'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->before(function ($records) {
                            // Handle children for bulk delete
                            foreach ($records as $record) {
                                if ($record->children()->exists()) {
                                    $record->children()->update(['parent_id' => $record->parent_id]);
                                }
                            }
                        }),
                ]),
            ])
            ->defaultSort('_lft', 'asc'); // Sort by nested set left value to maintain tree order
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\TasksRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWorkers::route('/'),
            'create' => Pages\CreateWorker::route('/create'),
            'view' => Pages\ViewWorker::route('/{record}'),
            'edit' => Pages\EditWorker::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        return 'primary';
    }
}
