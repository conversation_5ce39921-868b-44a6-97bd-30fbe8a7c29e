<?php

namespace App\Filament\Resources\WorkerResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TasksRelationManager extends RelationManager
{
    protected static string $relationship = 'tasks';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Textarea::make('description')
                    ->required()
                    ->rows(3)
                    ->columnSpanFull(),
                
                Forms\Components\Select::make('status_id')
                    ->label('Status')
                    ->options([
                        0 => 'Pending',
                        1 => 'In Progress',
                        2 => 'Completed',
                        3 => 'Cancelled',
                    ])
                    ->default(0)
                    ->required(),
                
                Forms\Components\FileUpload::make('files')
                    ->label('Task Files')
                    ->multiple()
                    ->directory('task-files')
                    ->acceptedFileTypes(['image/*', 'application/pdf', '.doc', '.docx'])
                    ->maxFiles(5),
                
                Forms\Components\Textarea::make('result')
                    ->label('Result/Notes')
                    ->rows(3)
                    ->columnSpanFull(),
                
                Forms\Components\FileUpload::make('result_files')
                    ->label('Result Files')
                    ->multiple()
                    ->directory('task-results')
                    ->acceptedFileTypes(['image/*', 'application/pdf', '.doc', '.docx'])
                    ->maxFiles(5),
                
                Forms\Components\Select::make('rate')
                    ->label('Rating')
                    ->options([
                        0 => 'Not Rated',
                        1 => '1 Star',
                        2 => '2 Stars',
                        3 => '3 Stars',
                        4 => '4 Stars',
                        5 => '5 Stars',
                    ])
                    ->default(0),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('description')
            ->columns([
                Tables\Columns\TextColumn::make('description')
                    ->label('Task Description')
                    ->limit(50)
                    ->searchable(),
                
                Tables\Columns\BadgeColumn::make('status_id')
                    ->label('Status')
                    ->formatStateUsing(fn ($state) => match($state) {
                        0 => 'Pending',
                        1 => 'In Progress',
                        2 => 'Completed',
                        3 => 'Cancelled',
                        default => 'Unknown'
                    })
                    ->colors([
                        'warning' => 0,
                        'primary' => 1,
                        'success' => 2,
                        'danger' => 3,
                    ]),
                
                Tables\Columns\TextColumn::make('rate')
                    ->label('Rating')
                    ->formatStateUsing(fn ($state) => $state > 0 ? str_repeat('⭐', $state) : 'Not Rated')
                    ->alignCenter(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status_id')
                    ->label('Status')
                    ->options([
                        0 => 'Pending',
                        1 => 'In Progress',
                        2 => 'Completed',
                        3 => 'Cancelled',
                    ]),
                
                Tables\Filters\SelectFilter::make('rate')
                    ->label('Rating')
                    ->options([
                        1 => '1 Star',
                        2 => '2 Stars',
                        3 => '3 Stars',
                        4 => '4 Stars',
                        5 => '5 Stars',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
