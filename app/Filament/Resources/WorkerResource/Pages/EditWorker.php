<?php

namespace App\Filament\Resources\WorkerResource\Pages;

use App\Filament\Resources\WorkerResource;
use App\Models\Worker;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Tables;
use Filament\Tables\Table;
use Livewire\Component as Livewire;

class EditWorker extends EditRecord
{
    protected static string $resource = WorkerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\Action::make('manage_children')
                ->label('Manage Children')
                ->icon('heroicon-o-user-group')
                ->color('info')
                ->modalHeading('Manage Child Workers')
                ->modalWidth('7xl')
                ->modalContent(view('filament.pages.manage-children', [
                    'record' => $this->record
                ]))
                ->modalActions([
                    Actions\Action::make('close')
                        ->label('Close')
                        ->color('gray')
                        ->close(),
                ]),
        ];
    }

    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction(),
            $this->getCancelFormAction(),
            Actions\Action::make('attach_child')
                ->label('Attach Child Worker')
                ->icon('heroicon-o-plus')
                ->color('success')
                ->form([
                    Forms\Components\Select::make('child_id')
                        ->label('Select Worker to Attach')
                        ->options(function () {
                            return Worker::query()
                                ->where('id', '!=', $this->record->id)
                                ->whereNotIn('id', $this->record->descendants()->pluck('id'))
                                ->orderBy('position')
                                ->pluck('position', 'id');
                        })
                        ->searchable()
                        ->required()
                        ->helperText('Choose which worker to attach as a child'),
                ])
                ->action(function (array $data) {
                    $child = Worker::find($data['child_id']);
                    if ($child) {
                        $child->appendToNode($this->record)->save();
                        Notification::make()
                            ->title('Child worker attached successfully')
                            ->success()
                            ->send();
                        $this->redirect($this->getResource()::getUrl('edit', ['record' => $this->record]));
                    }
                }),
        ];
    }
}
