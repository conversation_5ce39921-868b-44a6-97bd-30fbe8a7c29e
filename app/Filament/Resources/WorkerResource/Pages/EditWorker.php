<?php

namespace App\Filament\Resources\WorkerResource\Pages;

use App\Filament\Resources\WorkerResource;
use App\Models\Worker;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Str;

class EditWorker extends EditRecord
{
    protected static string $resource = WorkerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),

            Actions\DeleteAction::make()
                ->before(function (Worker $record) {
                    // Move children to parent before deleting
                    if ($record->children()->exists()) {
                        $record->children()->update(['parent_id' => $record->parent_id]);
                    }
                }),

            Actions\Action::make('add_child')
                ->label('Add Child Worker')
                ->icon('heroicon-o-plus')
                ->url(fn (Worker $record): string => route('filament.admin.resources.workers.create', ['parent' => $record->id]))
                ->color('success'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Auto-generate slug if position changed and slug is empty
        if (empty($data['slug']) && !empty($data['position'])) {
            $data['slug'] = Str::slug($data['position']);
        }

        return $data;
    }

    protected function afterSave(): void
    {
        $record = $this->record;
        $originalParentId = $record->getOriginal('parent_id');
        $newParentId = $record->parent_id;

        // If parent changed, move the node in the tree
        if ($originalParentId !== $newParentId) {
            if ($newParentId) {
                $newParent = Worker::find($newParentId);
                if ($newParent) {
                    $record->appendToNode($newParent)->save();
                }
            } else {
                // Move to root level
                $record->saveAsRoot();
            }
        }
    }
}
