<?php

namespace App\Filament\Resources\WorkerResource\Pages;

use App\Filament\Resources\WorkerResource;
use App\Models\Worker;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListWorkers extends ListRecords
{
    protected static string $resource = WorkerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Add Root Worker'),

            Actions\Action::make('expand_all')
                ->label('Expand All')
                ->icon('heroicon-o-plus-circle')
                ->color('gray')
                ->action(function () {
                    $this->dispatch('expand-all-tree-nodes');
                }),

            Actions\Action::make('collapse_all')
                ->label('Collapse All')
                ->icon('heroicon-o-minus-circle')
                ->color('gray')
                ->action(function () {
                    $this->dispatch('collapse-all-tree-nodes');
                }),
        ];
    }

    protected function getTableQuery(): Builder
    {
        // Get all workers with their depth for proper tree display
        return Worker::query()
            ->withDepth()
            ->orderBy('_lft'); // Order by nested set left value to maintain tree structure
    }

    protected function getHeaderWidgets(): array
    {
        return [
            WorkerResource\Widgets\WorkerStatsWidget::class,
        ];
    }
}
