<?php

namespace App\Filament\Resources\WorkerResource\Pages;

use App\Filament\Resources\WorkerResource;
use App\Models\Worker;
use Filament\Actions;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;

class ViewWorker extends ViewRecord
{
    protected static string $resource = WorkerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make()
                ->before(function (Worker $record) {
                    // Move children to parent before deleting
                    if ($record->children()->exists()) {
                        $record->children()->update(['parent_id' => $record->parent_id]);
                    }
                }),
            
            Actions\Action::make('add_child')
                ->label('Add Child Worker')
                ->icon('heroicon-o-plus')
                ->url(fn (Worker $record): string => route('filament.admin.resources.workers.create', ['parent' => $record->id]))
                ->color('success'),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Worker Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('position')
                            ->label('Position'),
                        
                        Infolists\Components\TextEntry::make('slug')
                            ->label('Slug'),
                        
                        Infolists\Components\TextEntry::make('description')
                            ->label('Description')
                            ->placeholder('No description provided'),
                        
                        Infolists\Components\TextEntry::make('user.name')
                            ->label('Assigned User')
                            ->placeholder('Not assigned'),
                        
                        Infolists\Components\TextEntry::make('reward')
                            ->label('Reward')
                            ->suffix('%'),
                    ])
                    ->columns(2),
                
                Infolists\Components\Section::make('Hierarchy Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('parent.position')
                            ->label('Parent Worker')
                            ->placeholder('Root level'),
                        
                        Infolists\Components\TextEntry::make('depth')
                            ->label('Depth Level'),
                        
                        Infolists\Components\TextEntry::make('children_count')
                            ->label('Direct Children')
                            ->getStateUsing(fn ($record) => $record->children()->count()),
                        
                        Infolists\Components\TextEntry::make('descendants_count')
                            ->label('Total Descendants')
                            ->getStateUsing(fn ($record) => $record->descendants()->count()),
                    ])
                    ->columns(2),
                
                Infolists\Components\Section::make('Statistics')
                    ->schema([
                        Infolists\Components\TextEntry::make('tasks_count')
                            ->label('Total Tasks')
                            ->getStateUsing(fn ($record) => $record->tasks()->count()),
                        
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Created')
                            ->dateTime(),
                        
                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Last Updated')
                            ->dateTime(),
                    ])
                    ->columns(3),
            ]);
    }

    protected function getFooterWidgets(): array
    {
        return [
            WorkerResource\Widgets\WorkerTreeWidget::class,
        ];
    }
}
