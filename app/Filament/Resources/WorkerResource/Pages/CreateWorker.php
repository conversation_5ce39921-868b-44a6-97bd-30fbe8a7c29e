<?php

namespace App\Filament\Resources\WorkerResource\Pages;

use App\Filament\Resources\WorkerResource;
use App\Models\Worker;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Str;

class CreateWorker extends CreateRecord
{
    protected static string $resource = WorkerResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Auto-generate slug if not provided
        if (empty($data['slug']) && !empty($data['position'])) {
            $data['slug'] = Str::slug($data['position']);
        }

        return $data;
    }

    protected function afterCreate(): void
    {
        $record = $this->record;
        $parentId = request()->get('parent');

        if ($parentId) {
            $parent = Worker::find($parentId);
            if ($parent) {
                // Use nested set to properly append as child
                $record->appendToNode($parent)->save();
            }
        }
    }

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction(),
            $this->getCreateAnotherFormAction(),
            $this->getCancelFormAction(),
        ];
    }

    public function getTitle(): string
    {
        $parentId = request()->get('parent');
        if ($parentId) {
            $parent = Worker::find($parentId);
            if ($parent) {
                return "Add Child Worker to: {$parent->position}";
            }
        }

        return 'Create Worker';
    }
}
