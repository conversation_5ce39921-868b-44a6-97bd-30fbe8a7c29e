<?php

namespace App\Filament\Resources\WorkerResource\Widgets;

use App\Models\Worker;
use App\Models\Task;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class WorkerStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $totalWorkers = Worker::count();
        $rootWorkers = Worker::whereIsRoot()->count();
        $workersWithUsers = Worker::whereNotNull('user_id')->count();
        $totalTasks = Task::count();
        $completedTasks = Task::where('status_id', 2)->count();
        $averageReward = Worker::avg('reward');

        return [
            Stat::make('Total Workers', $totalWorkers)
                ->description('All workers in the system')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary'),

            Stat::make('Root Workers', $rootWorkers)
                ->description('Top-level positions')
                ->descriptionIcon('heroicon-m-building-office')
                ->color('success'),

            Stat::make('Assigned Workers', $workersWithUsers)
                ->description('Workers with assigned users')
                ->descriptionIcon('heroicon-m-user-check')
                ->color('info'),

            Stat::make('Total Tasks', $totalTasks)
                ->description('All tasks across workers')
                ->descriptionIcon('heroicon-m-clipboard-document-list')
                ->color('warning'),

            Stat::make('Completed Tasks', $completedTasks)
                ->description($totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 1) . '% completion rate' : 'No tasks yet')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('Average Reward', number_format($averageReward, 3) . '%')
                ->description('Average reward percentage')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('primary'),
        ];
    }
}
