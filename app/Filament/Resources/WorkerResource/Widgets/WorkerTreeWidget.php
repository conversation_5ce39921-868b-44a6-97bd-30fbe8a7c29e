<?php

namespace App\Filament\Resources\WorkerResource\Widgets;

use App\Models\Worker;
use Filament\Notifications\Notification;
use InvadersXX\FilamentNestedList\Actions\Action;
use InvadersXX\FilamentNestedList\Actions\ActionGroup;
use InvadersXX\FilamentNestedList\Actions\DeleteAction;
use InvadersXX\FilamentNestedList\Actions\EditAction;
use InvadersXX\FilamentNestedList\Actions\ViewAction;
use InvadersXX\FilamentNestedList\Widgets\NestedList as BaseWidget;

class WorkerTreeWidget extends BaseWidget
{
    protected static string $model = Worker::class;

    protected static int $maxDepth = 10;

    protected ?string $treeTitle = 'Worker Hierarchy Tree';

    protected bool $enableTreeTitle = true;

    protected function getFormSchema(): array
    {
        return [
            \Filament\Forms\Components\TextInput::make('position')
                ->required()
                ->maxLength(255),
            
            \Filament\Forms\Components\TextInput::make('slug')
                ->required()
                ->maxLength(255),
            
            \Filament\Forms\Components\Textarea::make('description')
                ->rows(3),
            
            \Filament\Forms\Components\Select::make('user_id')
                ->relationship('user', 'name')
                ->searchable()
                ->preload(),
            
            \Filament\Forms\Components\TextInput::make('reward')
                ->numeric()
                ->step(0.001)
                ->suffix('%')
                ->required(),
        ];
    }

    public function getViewFormSchema(): array 
    {
        return [
            \Filament\Infolists\Components\TextEntry::make('position'),
            \Filament\Infolists\Components\TextEntry::make('slug'),
            \Filament\Infolists\Components\TextEntry::make('description'),
            \Filament\Infolists\Components\TextEntry::make('user.name')
                ->label('Assigned User'),
            \Filament\Infolists\Components\TextEntry::make('reward')
                ->suffix('%'),
        ];
    }

    public function getTreeRecordIcon(?\Illuminate\Database\Eloquent\Model $record = null): ?string
    {
        if (!$record) {
            return 'heroicon-o-user';
        }

        // Different icons based on hierarchy level or other criteria
        if ($record->isRoot()) {
            return 'heroicon-o-building-office';
        } elseif ($record->children()->exists()) {
            return 'heroicon-o-user-group';
        } else {
            return 'heroicon-o-user';
        }
    }

    protected function getTreeActions(): array
    {
        return [
            ViewAction::make()
                ->icon('heroicon-o-eye'),
            
            EditAction::make()
                ->icon('heroicon-o-pencil'),
            
            Action::make('add_child')
                ->label('Add Child')
                ->icon('heroicon-o-plus')
                ->action(function (array $data, $record) {
                    $child = new Worker($data);
                    $child->appendToNode($record)->save();
                    
                    Notification::make()
                        ->title('Child worker added successfully')
                        ->success()
                        ->send();
                })
                ->form($this->getFormSchema()),
            
            DeleteAction::make()
                ->icon('heroicon-o-trash')
                ->before(function ($record) {
                    // Move children to parent before deleting
                    if ($record->children()->exists()) {
                        $record->children()->update(['parent_id' => $record->parent_id]);
                    }
                }),
        ];
    }

    protected function getTreeRecordTitle($record): string
    {
        $title = $record->position;
        
        if ($record->user) {
            $title .= ' (' . $record->user->name . ')';
        }
        
        if ($record->children()->exists()) {
            $childrenCount = $record->children()->count();
            $title .= ' [' . $childrenCount . ' children]';
        }
        
        return $title;
    }

    protected function getTreeRecordDescription($record): ?string
    {
        $parts = [];
        
        if ($record->description) {
            $parts[] = \Illuminate\Support\Str::limit($record->description, 50);
        }
        
        if ($record->reward) {
            $parts[] = 'Reward: ' . $record->reward . '%';
        }
        
        $tasksCount = $record->tasks()->count();
        if ($tasksCount > 0) {
            $parts[] = $tasksCount . ' tasks';
        }
        
        return implode(' • ', $parts);
    }
}
